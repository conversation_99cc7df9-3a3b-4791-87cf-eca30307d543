#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL文件格式检测脚本
检测指定字段的数量一致性和格式合理性
"""

import json
import sys
from collections import defaultdict
from typing import Dict, Any


class JSONLValidator:
    """JSONL文件验证器"""

    def __init__(self, file_path: str):
        self.file_path = file_path
        self.required_fields = [
            "persona_id",
            "StrengthsAndResources",
            "SocialSupportSystem",
            "FormativeExperiences",
            "InterestsAndValues"
        ]
        self.errors = []
        self.field_counts = defaultdict(int)
        self.total_records = 0

    def validate_file(self) -> Dict[str, Any]:
        """验证整个文件"""
        print(f"开始检测文件: {self.file_path}")
        print("=" * 60)

        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 尝试解析为单个JSON对象
            try:
                data = json.loads(content)
                if isinstance(data, dict):
                    # 单个JSON对象
                    self._validate_single_record(data, 1)
                    self.total_records = 1
                elif isinstance(data, list):
                    # JSON数组
                    for i, record in enumerate(data, 1):
                        self._validate_single_record(record, i)
                    self.total_records = len(data)
                else:
                    self.errors.append("文件内容不是有效的JSON对象或数组")

            except json.JSONDecodeError:
                # 尝试按JSONL格式解析（每行一个JSON）
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        record = json.loads(line)
                        self._validate_single_record(record, line_num)
                        self.total_records += 1
                    except json.JSONDecodeError as e:
                        self.errors.append(f"第{line_num}行JSON格式错误: {str(e)}")

        except FileNotFoundError:
            self.errors.append(f"文件不存在: {self.file_path}")
        except Exception as e:
            self.errors.append(f"读取文件时发生错误: {str(e)}")

        return self._generate_report()

    def _validate_single_record(self, record: Dict[str, Any], record_num: int):
        """验证单条记录"""
        if not isinstance(record, dict):
            self.errors.append(f"记录{record_num}: 不是有效的JSON对象")
            return

        # 检查必需字段是否存在
        for field in self.required_fields:
            if field in record:
                self.field_counts[field] += 1
                self._validate_field_format(record, field, record_num)
            else:
                self.errors.append(f"记录{record_num}: 缺少必需字段 '{field}'")

    def _validate_field_format(self, record: Dict[str, Any], field: str, record_num: int):
        """验证字段格式"""
        value = record[field]

        if field == "persona_id":
            self._validate_persona_id(value, record_num)
        elif field == "StrengthsAndResources":
            self._validate_strengths_and_resources(value, record_num)
        elif field == "SocialSupportSystem":
            self._validate_social_support_system(value, record_num)
        elif field == "FormativeExperiences":
            self._validate_formative_experiences(value, record_num)
        elif field == "InterestsAndValues":
            self._validate_interests_and_values(value, record_num)

    def _validate_persona_id(self, value: Any, record_num: int):
        """验证persona_id格式"""
        if not isinstance(value, str):
            self.errors.append(f"记录{record_num}: persona_id应为字符串类型，实际为{type(value).__name__}")
            return

        if not value.strip():
            self.errors.append(f"记录{record_num}: persona_id不能为空")
            return

        # 检查是否为UUID格式（可选）
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, value.lower()):
            self.errors.append(f"记录{record_num}: persona_id格式不符合UUID标准: {value}")

    def _validate_strengths_and_resources(self, value: Any, record_num: int):
        """验证StrengthsAndResources格式"""
        if not isinstance(value, list):
            self.errors.append(f"记录{record_num}: StrengthsAndResources应为数组类型，实际为{type(value).__name__}")
            return

        if len(value) == 0:
            self.errors.append(f"记录{record_num}: StrengthsAndResources不能为空数组")
            return

        for i, item in enumerate(value):
            if not isinstance(item, str):
                self.errors.append(f"记录{record_num}: StrengthsAndResources[{i}]应为字符串类型")
            elif not item.strip():
                self.errors.append(f"记录{record_num}: StrengthsAndResources[{i}]不能为空字符串")

    def _validate_social_support_system(self, value: Any, record_num: int):
        """验证SocialSupportSystem格式"""
        if not isinstance(value, dict):
            self.errors.append(f"记录{record_num}: SocialSupportSystem应为对象类型，实际为{type(value).__name__}")
            return

        if len(value) == 0:
            self.errors.append(f"记录{record_num}: SocialSupportSystem不能为空对象")
            return

        for key, val in value.items():
            if not isinstance(key, str) or not key.strip():
                self.errors.append(f"记录{record_num}: SocialSupportSystem的键应为非空字符串")
            if not isinstance(val, str) or not val.strip():
                self.errors.append(f"记录{record_num}: SocialSupportSystem['{key}']的值应为非空字符串")

    def _validate_formative_experiences(self, value: Any, record_num: int):
        """验证FormativeExperiences格式"""
        if not isinstance(value, list):
            self.errors.append(f"记录{record_num}: FormativeExperiences应为数组类型，实际为{type(value).__name__}")
            return

        if len(value) == 0:
            self.errors.append(f"记录{record_num}: FormativeExperiences不能为空数组")
            return

        for i, item in enumerate(value):
            if not isinstance(item, dict):
                self.errors.append(f"记录{record_num}: FormativeExperiences[{i}]应为对象类型")
                continue

            # 检查是否包含"事件"字段
            if "事件" not in item:
                self.errors.append(f"记录{record_num}: FormativeExperiences[{i}]缺少'事件'字段")
            elif not isinstance(item["事件"], str) or not item["事件"].strip():
                self.errors.append(f"记录{record_num}: FormativeExperiences[{i}]['事件']应为非空字符串")

    def _validate_interests_and_values(self, value: Any, record_num: int):
        """验证InterestsAndValues格式"""
        if not isinstance(value, dict):
            self.errors.append(f"记录{record_num}: InterestsAndValues应为对象类型，实际为{type(value).__name__}")
            return

        if len(value) == 0:
            self.errors.append(f"记录{record_num}: InterestsAndValues不能为空对象")
            return

        # 检查是否包含Interests字段
        if "Interests" not in value:
            self.errors.append(f"记录{record_num}: InterestsAndValues缺少'Interests'字段")
        elif not isinstance(value["Interests"], list):
            self.errors.append(f"记录{record_num}: InterestsAndValues['Interests']应为数组类型")
        elif len(value["Interests"]) == 0:
            self.errors.append(f"记录{record_num}: InterestsAndValues['Interests']不能为空数组")

    def _generate_report(self) -> Dict[str, Any]:
        """生成检测报告"""
        report = {
            "file_path": self.file_path,
            "total_records": self.total_records,
            "field_counts": dict(self.field_counts),
            "errors": self.errors,
            "summary": {}
        }

        # 生成摘要
        if self.total_records > 0:
            expected_count = self.total_records
            inconsistent_fields = []

            for field in self.required_fields:
                actual_count = self.field_counts[field]
                if actual_count != expected_count:
                    inconsistent_fields.append({
                        "field": field,
                        "expected": expected_count,
                        "actual": actual_count,
                        "difference": actual_count - expected_count
                    })

            report["summary"] = {
                "total_errors": len(self.errors),
                "inconsistent_fields": inconsistent_fields,
                "is_valid": len(self.errors) == 0 and len(inconsistent_fields) == 0
            }

        return report


def print_report(report: Dict[str, Any]):
    """打印检测报告"""
    print(f"\n📊 检测报告")
    print("=" * 60)
    print(f"文件路径: {report['file_path']}")
    print(f"总记录数: {report['total_records']}")

    print(f"\n📈 字段统计:")
    for field, count in report['field_counts'].items():
        print(f"  {field}: {count}")

    # 字段数量一致性检查
    inconsistent = report['summary'].get('inconsistent_fields', [])
    if inconsistent:
        print(f"\n❌ 字段数量不一致问题:")
        for item in inconsistent:
            print(f"  {item['field']}: 期望{item['expected']}个，实际{item['actual']}个 (差异: {item['difference']:+d})")
    else:
        print(f"\n✅ 所有字段数量一致")

    # 格式错误
    errors = report['errors']
    if errors:
        print(f"\n❌ 格式错误 ({len(errors)}个):")
        for i, error in enumerate(errors[:20], 1):  # 只显示前20个错误
            print(f"  {i}. {error}")
        if len(errors) > 20:
            print(f"  ... 还有{len(errors) - 20}个错误未显示")
    else:
        print(f"\n✅ 未发现格式错误")

    # 总结
    print(f"\n📋 总结:")
    print(f"  总错误数: {report['summary'].get('total_errors', 0)}")
    print(f"  验证结果: {'✅ 通过' if report['summary'].get('is_valid', False) else '❌ 未通过'}")


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python invalid_detect.py <jsonl_file_path>")
        print("示例: python invalid_detect.py expand_0726_4000.jsonl")
        sys.exit(1)

    file_path = sys.argv[1]
    validator = JSONLValidator(file_path)
    report = validator.validate_file()
    print_report(report)

    # 如果有错误，返回非零退出码
    if not report['summary'].get('is_valid', False):
        sys.exit(1)


if __name__ == "__main__":
    main()