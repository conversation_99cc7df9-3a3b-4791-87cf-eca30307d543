#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试改进后的analyze_diff_2.py文件
"""

import json
import os
import tempfile
from analyze_diff_2 import (
    load_json_data, 
    comprehensive_json_analysis,
    initialize_jieba_dict,
    advanced_word_segmentation,
    analyze_word_frequency
)

def create_test_json_data():
    """创建测试用的JSON数据"""
    test_data = [
        {
            "Gender": "女性",
            "Age": 17,
            "Occupation": "高中生",
            "Topic": "婚恋情感",
            "Subtopic": "信任危机与沟通障碍",
            "Personality": [
                "焦虑",
                "依赖",
                "敏感",
                "讨好",
                "占有欲强"
            ],
            "Situation": "林晓与男朋友张明因对方与其他女生频繁聊天引发信任危机，沟通障碍导致反复冷战和分手威胁，她极度恐惧被抛弃。",
            "Event Time": "放学后",
            "Event Location": "学校附近的公园",
            "Event Participants": "林晓、张明",
            "Event Description": "林晓发现张明在手机上与其他女生亲密聊天，质问他时遭到冷淡回应。冲动下林晓提出分手并离开。几天后张明加回她哄劝复合，但当林晓要求删除聊天对象时，张明拖延并敷衍。林晓再次质疑关系可能性，张明冷漠回复'随便你'并拉黑所有联系方式。林晓陷入绝望，向闺蜜哭诉'我该怎么办？'，反复尝试联系张明未果。",
            "Emotional Experience Words": [
                "焦虑",
                "恐惧",
                "无助",
                "迷茫",
                "委屈"
            ],
            "Coped Strategies and Effects": "尝试直接沟通但情绪化指责激化矛盾；转向求助朋友倾诉，短暂缓解焦虑但未解决核心问题，反而因行动混乱加深关系裂痕。",
            "Goals and Expectations": "渴望修复恋爱关系以获得安全感，期望对方主动示好证明忠诚，同时亟需学会健康沟通以避免被抛弃。",
            "Core Drive": "安全感寻求者-被抛弃恐惧",
            "Reaction Pattern": "直接求助与迷茫探索型",
            "persona_id": "881f231e-80dc-48bb-ae55-d312f6901bac",
            "StrengthsAndResources": [
                "手工编织能力突出，能将复杂情感编织进绳结图案，曾获校级文创比赛二等奖",
                "对朋友极度忠诚，闺蜜失恋时连续一周每天手写安慰卡片",
                "语言感知力敏锐，能精准捕捉他人话语中的情绪变化，常被同学称为"人形情绪检测仪"",
                "抗压能力强，尽管内心焦虑仍能坚持完成学业，成绩保持班级中上游"
            ],
            "SocialSupportSystem": {
                "小雯": "同班闺蜜兼室友，理性务实，是林晓的"恋爱军师"，常一针见血指出问题但方式直接",
                "单亲母亲": "开社区花店独自抚养女儿，情感细腻但过度保护，林晓不敢完全坦白恋情细节",
                "张阿姨": "母亲花店的常客，退休心理教师，偶尔会以"过来人脸皮"给林晓讲些感情故事",
                "编织爱好者群": "200人规模的手工爱好者微信群，每周分享作品，林晓在群里叫"结绳记事""
            },
            "FormativeExperiences": [
                {
                    "事件": "9岁时参加钢琴比赛前夜，许诺会来加油的父亲因临时出差爽约，却被发现在朋友圈晒出带同事孩子游乐园照片",
                    "影响": "形成"亲近的人会用谎言代替真相"的认知，导致在恋爱中需要反复确认对方行踪来获得安全感"
                },
                {
                    "事件": "14岁生日收到编织材料包，附带卡片"你的手链让我想起外婆的温暖"，后发现是曾被她帮助过的转学生所赠",
                    "影响": "首次体会到非功利性的善意，这让她在友情中愿意无条件付出，但也产生"只有不断给予才不会被抛弃"的认知偏差"
                }
            ],
            "InterestsAndValues": {
                "Interests": [
                    "收集不同年代的情书手稿（尤其喜欢1980年代的钢笔字迹）",
                    "制作情绪手账（用不同材质的布料拼贴心情）",
                    "跟着老电影学做复古甜点"
                ],
                "Values": [
                    "言语必须兑现",
                    "亲密关系应保持"绝对透明"",
                    "礼物必须亲手制作才有意义"
                ]
            }
        }
    ]
    return test_data

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试基本功能...")
    
    # 初始化jieba词典
    initialize_jieba_dict()
    
    # 测试分词功能
    test_text = "心理咨询师建议她学会情绪管理和人际沟通技巧"
    words = advanced_word_segmentation(test_text, use_pos_tagging=True)
    print(f"分词测试: '{test_text}' -> {words}")
    
    # 测试词频分析
    word_freq = analyze_word_frequency(test_text, top_n=10)
    print(f"词频分析: {word_freq}")
    
    print("✅ 基本功能测试通过")

def test_json_analysis():
    """测试JSON分析功能"""
    print("\n🧪 开始测试JSON分析功能...")
    
    # 创建测试数据
    test_data = create_test_json_data()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
        temp_file = f.name
    
    try:
        # 测试数据加载
        loaded_data = load_json_data(temp_file)
        print(f"数据加载测试: 加载了 {len(loaded_data)} 条记录")
        
        # 测试全面分析
        with tempfile.TemporaryDirectory() as temp_dir:
            results = comprehensive_json_analysis(loaded_data, temp_dir)
            
            print(f"全面分析测试:")
            print(f"  - 总记录数: {results['total_records']}")
            print(f"  - 发现字段数: {len(results['field_analysis'])}")
            print(f"  - 生成词云数: {len(results['word_clouds'])}")
            
            # 检查一些关键字段
            key_fields = ['Personality', 'StrengthsAndResources', 'SocialSupportSystem']
            for field in key_fields:
                if field in results['field_analysis']:
                    field_data = results['field_analysis'][field]
                    print(f"  - {field}: {field_data['with_keys']['total_words']} 词")
        
        print("✅ JSON分析功能测试通过")
        
    finally:
        # 清理临时文件
        os.unlink(temp_file)

def main():
    """主测试函数"""
    print("🚀 开始测试改进后的analyze_diff_2.py")
    print("="*60)
    
    try:
        test_basic_functionality()
        test_json_analysis()
        
        print("\n🎉 所有测试通过！改进后的代码工作正常。")
        print("\n📋 主要改进点:")
        print("  ✅ 改进了分词器，减少漏词问题")
        print("  ✅ 支持包含JSON字段名的分析")
        print("  ✅ 增强了对不同数据格式的处理")
        print("  ✅ 提供了全面的词频统计和词云生成")
        print("  ✅ 改进了用户界面和结果展示")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
